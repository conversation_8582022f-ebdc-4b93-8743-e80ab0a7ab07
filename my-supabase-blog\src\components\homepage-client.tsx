'use client'

import { useState, useMemo, useEffect } from 'react'
import Link from 'next/link'
import { Post } from '@/types/database'
import { PostCard } from '@/components/post-card'
import { SearchBar, SearchResults } from '@/components/search-bar'
import { Pagination, PaginationInfo } from '@/components/pagination'
import { searchPosts, paginateItems } from '@/lib/utils'
import { ClientThemeToggle } from '@/components/client-theme-toggle'

interface HomePageClientProps {
  initialPosts: Post[]
  user: { id: string; email?: string } | null
  userIsAdmin: boolean
}

const POSTS_PER_PAGE = 6

export function HomePageClient({ initialPosts: posts, user, userIsAdmin }: HomePageClientProps) {
  console.log('HomePageClient received posts:', posts);
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)

  // Filter posts based on search query and category
  const filteredPosts = useMemo(() => {
    console.log('Filtering posts. searchQuery:', searchQuery);
    const result = searchPosts(posts, searchQuery);
    console.log('Filtered posts count:', result.length);
    return result;
  }, [posts, searchQuery])

  // Paginate filtered posts
  const paginatedData = useMemo(() => {
    console.log('Paginating posts. filteredPosts count:', filteredPosts.length, 'currentPage:', currentPage);
    const result = paginateItems(filteredPosts, currentPage, POSTS_PER_PAGE);
    console.log('Paginated items count:', result.items.length, 'Total pages:', result.totalPages);
    return result;
  }, [filteredPosts, currentPage])

  // Reset to first page when search or category changes
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }


  // Get featured post (most recent post)
  const featuredPost = posts.length > 0 ? posts[0] : null


  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card/95 shadow-sm border-b border-border sticky top-0 z-50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8 py-3 sm:py-4 lg:py-6">
          <div className="flex justify-between items-center">
            <Link href="/" className="text-xl sm:text-2xl lg:text-3xl font-bold text-foreground hover:text-primary transition-colors">
              My Blog
            </Link>
            <div className="flex items-center gap-1 sm:gap-2 lg:gap-4">
              <ClientThemeToggle />
              {user ? (
                <>
                  <span className="hidden lg:block text-sm text-muted-foreground truncate max-w-32">
                    Welcome, {user.email}
                  </span>
                  {userIsAdmin && (
                    <Link
                      href="/admin/new-post"
                      className="bg-primary text-primary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center"
                    >
                      <span className="hidden sm:inline">New Post</span>
                      <span className="sm:hidden text-lg">+</span>
                    </Link>
                  )}
                  {userIsAdmin && (
                    <Link
                      href="/admin/manage-posts"
                      className="bg-secondary text-secondary-foreground px-2 sm:px-3 lg:px-4 py-2 rounded-md hover:bg-secondary/80 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center"
                    >
                      <span className="hidden sm:inline">Manage</span>
                      <span className="sm:hidden text-lg">⚙️</span>
                    </Link>
                  )}
                  <form action="/auth/signout" method="post">
                    <button
                      type="submit"
                      className="inline-flex items-center justify-center px-2 sm:px-3 py-2 rounded-md text-sm font-medium transition-colors bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm hover:shadow-md touch-manipulation min-h-[44px]"
                    >
                      <svg className="w-4 h-4 sm:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                      </svg>
                      <span className="hidden sm:inline">Sign Out</span>
                    </button>
                  </form>
                </>
              ) : (
                <Link
                  href="/login"
                  className="bg-primary text-primary-foreground px-3 sm:px-4 py-2 rounded-md hover:bg-primary/90 transition-all duration-200 text-sm font-medium shadow-sm hover:shadow-md touch-manipulation min-h-[44px] flex items-center justify-center"
                >
                  Sign In
                </Link>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-3 sm:px-6 lg:px-8">
        {posts.length === 0 ? (
          /* Empty State */
          <div className="text-center py-16 lg:py-24">
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 mx-auto mb-6 bg-muted rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h2 className="text-2xl font-semibold text-foreground mb-2">No posts yet</h2>
              <p className="text-muted-foreground mb-6">Start sharing your thoughts with the world!</p>
              {userIsAdmin && (
                <Link
                  href="/admin/new-post"
                  className="inline-flex items-center px-6 py-3 bg-primary text-primary-foreground rounded-lg hover:bg-primary/90 transition-all duration-200 font-medium shadow-sm hover:shadow-md"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  Create your first post
                </Link>
              )}
            </div>
          </div>
        ) : (
          <>
            {console.log('Rendering posts. Total posts:', posts.length, 'Filtered posts:', filteredPosts.length, 'Paginated items:', paginatedData.items.length)}
            {/* Hero Section */}
            {/* Featured Post Section */}
            {featuredPost && (
              <section className="mb-16">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-foreground mb-2">Featured Post</h2>
                  <p className="text-muted-foreground">Don't miss our latest featured content</p>
                </div>
                <PostCard post={featuredPost} featured={true} />
              </section>
            )}

            {/* Search and Filter Section */}
            <section id="posts" className="mb-8 sm:mb-12">
              <div className="flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8">
                <div className="text-center sm:text-left">
                  <h2 className="text-2xl sm:text-3xl font-bold text-foreground mb-2">All Posts</h2>
                  <p className="text-muted-foreground">
                    {posts.length} post{posts.length === 1 ? '' : 's'} available
                  </p>
                </div>
                <div className="w-full sm:max-w-md sm:mx-auto lg:mx-0 lg:max-w-96">
                  <SearchBar onSearch={handleSearch} />
                </div>
              </div>


              {/* Search Results Info */}
              <SearchResults query={searchQuery} totalResults={filteredPosts.length} />
            </section>

            {/* Posts Grid */}
            <section className="mb-16">
              {paginatedData.items.length === 0 ? (
                <div className="text-center py-20">
                  <div className="w-20 h-20 mx-auto mb-8 bg-gradient-to-br from-muted to-muted/50 rounded-2xl flex items-center justify-center">
                    <svg className="w-10 h-10 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-semibold text-foreground mb-3">No posts found</h3>
                  <p className="text-muted-foreground text-lg mb-6">Try adjusting your search terms or browse all posts</p>
                  <Link
                    href="#posts"
                    onClick={() => {
                      setSearchQuery('')
                      setCurrentPage(1)
                    }}
                    className="inline-flex items-center px-6 py-3 rounded-lg bg-primary text-primary-foreground font-medium hover:bg-primary/90 transition-colors"
                  >
                    Clear Search
                  </Link>
                </div>
              ) : (
                <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3">
                  {paginatedData.items
                    .filter(post => post.id !== featuredPost?.id)
                    .map((post, index) => (
                    <PostCard
                      key={post.id}
                      post={post}
                      className="animate-fade-in hover:scale-[1.02] transition-transform duration-300"
                      style={{
                        animationDelay: `${index * 0.1}s`,
                        animationFillMode: 'both'
                      } as React.CSSProperties}
                    />
                  ))}
                </div>
              )}
            </section>

            {/* Pagination */}
            {paginatedData.totalPages > 1 && (
              <section className="mb-12 space-y-6">
                <Pagination
                  currentPage={paginatedData.currentPage}
                  totalPages={paginatedData.totalPages}
                  onPageChange={setCurrentPage}
                />
                <PaginationInfo
                  currentPage={paginatedData.currentPage}
                  totalPages={paginatedData.totalPages}
                  totalItems={paginatedData.totalItems}
                  itemsPerPage={POSTS_PER_PAGE}
                />
              </section>
            )}
          </>
        )}
      </main>
    </div>
  )
}
