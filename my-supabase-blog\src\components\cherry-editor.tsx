'use client'

import dynamic from 'next/dynamic'
import { useState, useEffect } from 'react'

// Dynamically import MDEditor to avoid SSR issues
const MDEditor = dynamic(
  () => import('@uiw/react-md-editor'),
  {
    ssr: false,
    loading: () => (
      <div className="w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse min-h-[400px] flex items-center justify-center">
        <div className="text-muted-foreground">Loading editor...</div>
      </div>
    )
  }
)

interface CherryEditorProps {
  value?: string
  onChange?: (value: string) => void
  height?: string
  placeholder?: string
}

export function CherryEditor({
  value = '',
  onChange,
  height = '500px',
  placeholder = 'Start writing your amazing content...'
}: CherryEditorProps) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Show loading state during SSR
  if (!mounted) {
    return (
      <div
        className="w-full p-4 border border-input bg-background text-foreground rounded-md animate-pulse flex items-center justify-center"
        style={{ height }}
      >
        <div className="text-muted-foreground">Loading editor...</div>
      </div>
    )
  }

  // Debug: Add console log to check if component is rendering
  console.log('CherryEditor rendering with value:', value)

  // Simple test to see if the issue is with MDEditor
  if (process.env.NODE_ENV === 'development') {
    return (
      <div className="w-full p-4 border border-input bg-background text-foreground rounded-md">
        <div className="mb-4">
          <h3 className="text-lg font-semibold mb-2">Debug: Editor Test</h3>
          <p className="text-sm text-muted-foreground mb-4">
            This is a test to see if the component is rendering. If you can see this, the component is working.
          </p>
        </div>
        <textarea
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          className="w-full bg-background text-foreground border border-input rounded-md p-3 resize-none focus:outline-none focus:ring-2 focus:ring-ring"
          style={{ height: `calc(${height} - 8rem)`, minHeight: '300px' }}
        />
        <div className="mt-4 p-3 bg-muted rounded-md">
          <h4 className="font-medium mb-2">Preview:</h4>
          <div className="text-sm whitespace-pre-wrap">{value || 'No content yet...'}</div>
        </div>
      </div>
    )
  }

  try {
    return (
      <div
        className="w-full md-editor-wrapper"
        style={{ height }}
        data-color-mode="auto"
      >
        <MDEditor
          value={value}
          onChange={(val) => onChange?.(val || '')}
          height={parseInt(height.replace('px', ''))}
          preview="live"
          hideToolbar={false}
          visibleDragbar={false}
          data-color-mode="auto"
          textareaProps={{
            placeholder,
            style: {
              fontSize: 14,
              lineHeight: 1.6,
              fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
              backgroundColor: 'transparent',
              color: 'inherit'
            }
          }}
          style={{
            backgroundColor: 'hsl(var(--background))',
            color: 'hsl(var(--foreground))'
          }}
        />
      </div>
    )
  } catch (error) {
    console.error('Error rendering MDEditor:', error)
    return (
      <div className="w-full p-4 border border-input bg-background text-foreground rounded-md">
        <div className="text-red-500 mb-2">Error loading editor</div>
        <textarea
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          className="w-full h-full bg-transparent border-0 outline-0 resize-none text-foreground"
          style={{ height: `calc(${height} - 2rem)` }}
        />
      </div>
    )
  }
}
